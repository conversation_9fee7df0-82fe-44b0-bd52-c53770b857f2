"""Main application entry point for workflow adapter."""

import logging
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from routers.provider import router as provider_router
from services.proxy import provider_proxy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    logger.info("Starting workflow adapter service...")
    yield
    logger.info("Shutting down workflow adapter service...")
    await provider_proxy.close()


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    app = FastAPI(
        title="Workflow Adapter",
        description="Centralized service to adapt FastAPI services to standardized format",
        version="0.1.0",
        lifespan=lifespan
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers
    app.include_router(provider_router, prefix="/api/v1")

    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "Workflow Adapter Service",
            "version": "0.1.0",
            "docs": "/docs"
        }

    return app


def main():
    """Main entry point."""
    app = create_app()
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8004,
        log_level="info"
    )


if __name__ == "__main__":
    main()
