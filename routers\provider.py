"""Provider-specific API routes for workflow adapter."""

import logging
from typing import Dict

from fastapi import APIRouter, HTTPException, Path, status

from config.providers import get_provider, provider_exists
from models.onboarding import OnboardingInfoResponse, RunRequest, RunResponse, StatusResponse
from services.job_manager import job_manager
from services.openapi_generator import generate_synthetic_openapi_spec
from services.proxy import provider_proxy

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/{provider_id}/openapi.json")
async def get_provider_openapi_spec(
    provider_id: str = Path(..., description="Unique identifier for the provider")
):
    """Get synthetic OpenAPI specification for a provider.

    This endpoint returns a synthetic OpenAPI spec that presents the provider's
    functionality in the standardized format expected by target services.
    """
    if not provider_exists(provider_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Provider '{provider_id}' not found"
        )

    try:
        provider = get_provider(provider_id)
        spec = generate_synthetic_openapi_spec(provider)
        return spec

    except Exception as e:
        logger.error(f"Error generating OpenAPI spec for {provider_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/{provider_id}/onboarding-info", response_model=OnboardingInfoResponse)
async def get_onboarding_info(
    provider_id: str = Path(..., description="Unique identifier for the provider")
):
    """Get onboarding information for a provider.
    
    This endpoint returns standardized onboarding information that the target
    service uses to validate and onboard the provider.
    """
    if not provider_exists(provider_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Provider '{provider_id}' not found"
        )
    
    try:
        provider = get_provider(provider_id)
        
        # Perform health check
        is_healthy = await provider_proxy.health_check(provider)
        if not is_healthy:
            logger.warning(f"Provider {provider_id} failed health check")
        
        return OnboardingInfoResponse(
            service_name=provider.service_name,
            openapi_url=provider.openapi_url,
            health_check_url=provider.health_check_url,
            description=provider.description,
            version=provider.version,
            input_schema=provider.input_schema
        )
        
    except Exception as e:
        logger.error(f"Error getting onboarding info for {provider_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/{provider_id}/run", response_model=RunResponse)
async def run_workflow(
    request: RunRequest,
    provider_id: str = Path(..., description="Unique identifier for the provider")
):
    """Run a workflow on the specified provider.
    
    This endpoint proxies the workflow execution request to the actual provider
    service and returns a standardized response with an adapter job ID.
    """
    if not provider_exists(provider_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Provider '{provider_id}' not found"
        )
    
    try:
        provider = get_provider(provider_id)
        
        # Run workflow on provider
        provider_job_id, job_status = await provider_proxy.run_workflow(
            provider, request.inputs
        )
        
        # Create job mapping
        adapter_job_id = job_manager.create_job_mapping(provider_id, provider_job_id)
        
        logger.info(
            f"Created job mapping: adapter={adapter_job_id}, "
            f"provider={provider_id}, provider_job={provider_job_id}"
        )
        
        return RunResponse(
            job_id=adapter_job_id,
            status=job_status
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running workflow for {provider_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/{provider_id}/status/{job_id}", response_model=StatusResponse)
async def get_job_status(
    provider_id: str = Path(..., description="Unique identifier for the provider"),
    job_id: str = Path(..., description="Job ID returned by the run endpoint")
):
    """Get the status of a job.
    
    This endpoint proxies the status check request to the actual provider
    service using the job ID mapping.
    """
    if not provider_exists(provider_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Provider '{provider_id}' not found"
        )
    
    # Get job mapping
    job_mapping = job_manager.get_job_mapping(job_id)
    if not job_mapping:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Job not found"
        )
    
    # Verify provider ID matches
    if job_mapping.provider_id != provider_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Job does not belong to the specified provider"
        )
    
    try:
        provider = get_provider(provider_id)
        
        # Get status from provider
        status_response = await provider_proxy.get_job_status(
            provider, job_mapping.provider_job_id
        )
        
        # Update job status in mapping
        job_manager.update_job_status(job_id, status_response.status)
        
        # Return response with adapter job ID
        status_response.job_id = job_id
        return status_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {provider_id}/{job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/providers", response_model=Dict[str, str])
async def list_providers():
    """List all available providers.
    
    This is a utility endpoint for debugging and management purposes.
    """
    from config.providers import list_providers
    return list_providers()


@router.get("/health")
async def health_check():
    """Health check endpoint for the adapter service."""
    job_count = job_manager.get_job_count()
    return {
        "status": "healthy",
        "active_jobs": job_count,
        "service": "workflow-adapter"
    }
