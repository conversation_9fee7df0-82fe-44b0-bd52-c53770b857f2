"""Hard-coded provider configurations for workflow adapter."""

from typing import Dict

from models.onboarding import ServiceInputSchemaProperty
from models.provider import ProviderAuth, ProviderConfig


# Example provider configurations
PROVIDERS: Dict[str, ProviderConfig] = {
    "text-processor": ProviderConfig(
        service_name="Text Processing Service",
        base_url="https://text-api.example.com",
        openapi_url="https://text-api.example.com/openapi.json",
        health_check_url="https://text-api.example.com/health",
        run_endpoint="/process",
        status_endpoint="/status/{job_id}",
        description="Service for processing text inputs",
        version="1.0.0",
        provider_type="fastapi",
        auth=ProviderAuth(
            type="bearer",
            token="example-bearer-token"
        ),
        input_schema=[
            ServiceInputSchemaProperty(
                label="文本",
                type="text-input",
                required=True,
                variable_name="text",
                description="Input text to process"
            ),
            ServiceInputSchemaProperty(
                label="处理模式",
                type="select",
                required=False,
                variable_name="mode",
                description="Processing mode",
                default="standard",
                enum=["standard", "advanced", "quick"]
            )
        ]
    ),
    
    "image-analyzer": ProviderConfig(
        service_name="Image Analysis Service",
        base_url="https://image-api.example.com",
        openapi_url="https://image-api.example.com/openapi.json",
        health_check_url="https://image-api.example.com/health",
        run_endpoint="/analyze",
        status_endpoint="/jobs/{job_id}/status",
        description="Service for analyzing images",
        version="2.1.0",
        provider_type="fastapi",
        auth=ProviderAuth(
            type="api_key",
            key_name="X-API-Key",
            key_value="example-api-key"
        ),
        input_schema=[
            ServiceInputSchemaProperty(
                label="图片URL",
                type="text-input",
                required=True,
                variable_name="image_url",
                description="URL of the image to analyze"
            ),
            ServiceInputSchemaProperty(
                label="分析类型",
                type="checkbox",
                required=False,
                variable_name="analysis_types",
                description="Types of analysis to perform",
                enum=["objects", "faces", "text", "colors"]
            )
        ]
    ),
    
    "dify-workflow": ProviderConfig(
        service_name="Dify Workflow Service",
        base_url="https://dify.example.com",
        openapi_url="https://dify.example.com/openapi.json",
        health_check_url="https://dify.example.com/health",
        run_endpoint="/workflows/run",
        status_endpoint="/tasks/{job_id}",
        description="Dify-based workflow service",
        version="1.5.0",
        provider_type="dify",
        auth=ProviderAuth(
            type="bearer",
            token="dify-api-token"
        ),
        input_schema=[
            ServiceInputSchemaProperty(
                label="输入数据",
                type="textarea",
                required=True,
                variable_name="inputs",
                description="Input data for the workflow"
            )
        ]
    ),

    "tender-docs-analysis":ProviderConfig(
        service_name = "招标文件解析",
        base_url = "http://**************:9507",
        openapi_url = "http://**************:9507/openapi.json",
        health_check_url = "http://**************:9507/api/v1/tool/heartbeat",
        run_endpoint = "/api/v1/llm/submit_file",
        status_endpoint = "/api/v1/llm/query",
        description = "招标文件解析服务",
        version = "1.0.0",
        provider_type = "fastapi",
        input_schema = [
            ServiceInputSchemaProperty(
                label="文件独特 id",
                type="text-input",
                required=True,
                variable_name="file_unique_id",
                description="文件的独特 id"
            ),
            ServiceInputSchemaProperty(
                label="提取目标",   
                type="text-input",
                required=False,
                variable_name="extract_targets",
                description="提取目标"
            )
        ]
    )
}


def get_provider(provider_id: str) -> ProviderConfig:
    """Get provider configuration by ID.
    
    Args:
        provider_id: Unique identifier for the provider
        
    Returns:
        Provider configuration
        
    Raises:
        KeyError: If provider not found
    """
    if provider_id not in PROVIDERS:
        raise KeyError(f"Provider '{provider_id}' not found")
    return PROVIDERS[provider_id]


def list_providers() -> Dict[str, str]:
    """List all available providers.
    
    Returns:
        Dictionary mapping provider IDs to service names
    """
    return {
        provider_id: config.service_name 
        for provider_id, config in PROVIDERS.items()
    }


def provider_exists(provider_id: str) -> bool:
    """Check if a provider exists.
    
    Args:
        provider_id: Unique identifier for the provider
        
    Returns:
        True if provider exists, False otherwise
    """
    return provider_id in PROVIDERS
