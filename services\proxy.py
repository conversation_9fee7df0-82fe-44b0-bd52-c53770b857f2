"""Proxy service for communicating with provider services."""

import logging
from typing import Any, Dict, Optional, Tuple

import httpx
from fastapi import HTT<PERSON>Ex<PERSON>, status

from models.onboarding import RunR<PERSON>ponse, StatusResponse
from models.provider import ProviderAuth, ProviderConfig

logger = logging.getLogger(__name__)


class ProviderProxy:
    """Handles communication with provider services."""
    
    def __init__(self):
        """Initialize the provider proxy."""
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
    
    def _build_headers(self, auth: Optional[ProviderAuth]) -> Dict[str, str]:
        """Build HTTP headers for provider requests.
        
        Args:
            auth: Authentication configuration
            
        Returns:
            Dictionary of HTTP headers
        """
        headers = {"Content-Type": "application/json"}
        
        if not auth:
            return headers
        
        auth_type = auth.type.lower()
        if auth_type == "bearer" and auth.token:
            headers["Authorization"] = f"Bearer {auth.token}"
        elif auth_type == "basic" and auth.username and auth.password:
            import base64
            auth_str = base64.b64encode(f"{auth.username}:{auth.password}".encode()).decode()
            headers["Authorization"] = f"Basic {auth_str}"
        elif auth_type == "api_key" and auth.key_name and auth.key_value:
            headers[auth.key_name] = auth.key_value
        
        return headers
    
    async def health_check(self, provider: ProviderConfig) -> bool:
        """Perform health check on provider service.
        
        Args:
            provider: Provider configuration
            
        Returns:
            True if healthy, False otherwise
        """
        try:
            headers = self._build_headers(provider.auth)
            response = await self.client.get(provider.health_check_url, headers=headers)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Health check failed for {provider.service_name}: {e}")
            return False
    
    async def run_workflow(
        self, 
        provider: ProviderConfig, 
        inputs: Dict[str, Any]
    ) -> Tuple[str, str]:
        """Run a workflow on the provider service.
        
        Args:
            provider: Provider configuration
            inputs: Input data for the workflow
            
        Returns:
            Tuple of (provider_job_id, status)
            
        Raises:
            HTTPException: If the request fails
        """
        try:
            headers = self._build_headers(provider.auth)
            url = f"{provider.base_url}{provider.run_endpoint}"
            
            # Transform inputs based on provider type
            payload = self._transform_run_request(provider, inputs)
            logger.info(f"Sending request to {url} with payload: {payload}")
            print(payload, url, flush=True)

            response = await self.client.post(url, json=payload, headers=headers)
            
            if response.status_code not in [200, 201]:
                logger.error(f"Provider request failed with status {response.status_code}: {response.text}")
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail=f"Provider request failed: {response.text}"
                )
            
            result = response.json()
            
            # Extract job ID based on provider type
            print(result,flush=True)
            job_id = self._extract_job_id(provider, result)
            job_status = result.get("status", "running")
            print(job_id,flush=True)
            return job_id, job_status
            
        except httpx.RequestError as e:
            logger.error(f"Request error for {provider.service_name}: {e}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"Failed to connect to provider: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error for {provider.service_name}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal error: {str(e)}"
            )
    
    async def get_job_status(
        self,
        provider: ProviderConfig,
        provider_job_id: str
    ) -> StatusResponse:
        """Get job status from provider service.

        Args:
            provider: Provider configuration
            provider_job_id: Job ID from the provider

        Returns:
            Status response

        Raises:
            HTTPException: If the request fails
        """
        try:
            headers = self._build_headers(provider.auth)

            if provider.provider_type == "tender_analysis":
                # For tender analysis, use POST request with event_id in body
                url = f"{provider.base_url}{provider.status_endpoint}"
                payload = {"event_id": provider_job_id}
                response = await self.client.post(url, json=payload, headers=headers)
            else:
                # Standard GET request with job ID in URL
                status_url = provider.status_endpoint.replace("{job_id}", provider_job_id)
                url = f"{provider.base_url}{status_url}"
                response = await self.client.get(url, headers=headers)

            if response.status_code == 404:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Job not found"
                )
            elif response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail=f"Provider request failed: {response.text}"
                )

            result = response.json()

            # Transform response based on provider type
            return self._transform_status_response(provider, provider_job_id, result)

        except HTTPException:
            raise
        except httpx.RequestError as e:
            logger.error(f"Request error for {provider.service_name}: {e}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"Failed to connect to provider: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error for {provider.service_name}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal error: {str(e)}"
            )
    
    def _transform_run_request(self, provider: ProviderConfig, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Transform run request based on provider type.

        Args:
            provider: Provider configuration
            inputs: Original input data

        Returns:
            Transformed input data
        """
        if provider.provider_type == "dify":
            return {
                "inputs": inputs,
                "response_mode": "blocking",
                "user": "workflow-adapter"
            }
        elif provider.provider_type == "coze":
            return {
                "workflow_id": inputs.get("workflow_id"),
                "parameters": inputs
            }
        elif provider.provider_type == "tender_analysis":
            # Transform for tender analysis service
            return inputs
        else:
            # FastAPI or default format
            return inputs
    
    def _extract_job_id(self, provider: ProviderConfig, response: Dict[str, Any]) -> str:
        """Extract job ID from provider response.

        Args:
            provider: Provider configuration
            response: Provider response data

        Returns:
            Job ID

        Raises:
            ValueError: If job ID cannot be extracted
        """
        if provider.provider_type == "dify":
            if "task_id" in response:
                return response["task_id"]
        elif provider.provider_type == "coze":
            if "execute_id" in response:
                return response["execute_id"]
        elif provider.provider_type == "tender_analysis":
            # For tender analysis, extract event_id from data field
            if "data" in response and isinstance(response["data"], dict):
                if "event_id" in response["data"]:
                    return response["data"]["event_id"]
            # Also check direct event_id field
            if "event_id" in response:
                return response["event_id"]

        # Default: look for common job ID fields
        for field in ["job_id", "task_id", "id", "execution_id", "event_id"]:
            if field in response:
                return str(response[field])

        raise ValueError("Could not extract job ID from provider response")
    
    def _transform_status_response(
        self,
        provider: ProviderConfig,
        job_id: str,
        response: Dict[str, Any]
    ) -> StatusResponse:
        """Transform status response based on provider type.

        Args:
            provider: Provider configuration
            job_id: Job ID
            response: Provider response data

        Returns:
            Standardized status response
        """
        if provider.provider_type == "tender_analysis":
            # Handle tender analysis response format
            data = response.get("data", {})
            status_value = data.get("status", "unknown")

            # Normalize tender analysis status values
            if status_value == "complete":
                status_value = "completed"
            elif status_value == "fail":
                status_value = "failed"
            elif status_value in ["deferred", "queued"]:
                status_value = "queued"
            elif status_value == "in_progress":
                status_value = "running"
            elif status_value == "not_found":
                status_value = "failed"

            return StatusResponse(
                job_id=job_id,
                status=status_value,
                result=data.get("items") if status_value == "completed" else None,
                error=data.get("error"),
                progress=None  # Tender analysis doesn't provide progress
            )
        else:
            # Standard response format
            status_value = response.get("status", "unknown")

            # Normalize status values
            if status_value in ["completed", "success", "finished"]:
                status_value = "completed"
            elif status_value in ["failed", "error"]:
                status_value = "failed"
            elif status_value in ["running", "in_progress", "processing"]:
                status_value = "running"

            return StatusResponse(
                job_id=job_id,
                status=status_value,
                result=response.get("result") or response.get("data"),
                error=response.get("error") or response.get("error_message"),
                progress=response.get("progress")
            )


# Global proxy instance
provider_proxy = ProviderProxy()
