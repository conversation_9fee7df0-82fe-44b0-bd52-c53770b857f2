"""OpenAPI specification generator for providers."""

from typing import Any, Dict

from models.provider import ProviderConfig


def generate_synthetic_openapi_spec(provider: ProviderConfig) -> Dict[str, Any]:
    """Generate a synthetic OpenAPI spec for a provider that conforms to expected format.
    
    Args:
        provider: Provider configuration
        
    Returns:
        Synthetic OpenAPI specification
    """
    # Base OpenAPI spec structure
    spec = {
        "openapi": "3.1.0",
        "info": {
            "title": provider.service_name,
            "description": provider.description or f"Workflow service for {provider.service_name}",
            "version": provider.version or "1.0.0"
        },
        "paths": {
            "/run": {
                "post": {
                    "summary": "Run workflow",
                    "description": "Execute a workflow with the provided inputs",
                    "operationId": "run_workflow",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": _generate_input_schema(provider)
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Workflow started successfully",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "job_id": {
                                                "type": "string",
                                                "description": "Unique identifier for the job"
                                            },
                                            "status": {
                                                "type": "string",
                                                "description": "Initial status of the job",
                                                "default": "running"
                                            }
                                        },
                                        "required": ["job_id"]
                                    }
                                }
                            }
                        },
                        "400": {
                            "description": "Bad request"
                        },
                        "500": {
                            "description": "Internal server error"
                        }
                    }
                }
            },
            "/status/{job_id}": {
                "parameters": [
                    {
                        "name": "job_id",
                        "in": "path",
                        "required": True,
                        "schema": {
                            "type": "string"
                        },
                        "description": "Job ID returned by the run endpoint"
                    }
                ],
                "get": {
                    "summary": "Get job status",
                    "description": "Get the current status of a job",
                    "operationId": "get_job_status",
                    "responses": {
                        "200": {
                            "description": "Job status retrieved successfully",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "job_id": {
                                                "type": "string",
                                                "description": "Job identifier"
                                            },
                                            "status": {
                                                "type": "string",
                                                "description": "Current status of the job",
                                                "enum": ["running", "completed", "failed", "queued"]
                                            },
                                            "result": {
                                                "type": "object",
                                                "description": "Job result if completed",
                                                "nullable": True
                                            },
                                            "error": {
                                                "type": "string",
                                                "description": "Error message if failed",
                                                "nullable": True
                                            },
                                            "progress": {
                                                "type": "number",
                                                "description": "Progress percentage (0-100)",
                                                "nullable": True
                                            }
                                        },
                                        "required": ["job_id", "status"]
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "Job not found"
                        },
                        "500": {
                            "description": "Internal server error"
                        }
                    }
                }
            }
        },
        "components": {
            "schemas": {}
        }
    }
    
    return spec


def _generate_input_schema(provider: ProviderConfig) -> Dict[str, Any]:
    """Generate input schema for the provider.
    
    Args:
        provider: Provider configuration
        
    Returns:
        JSON schema for the input
    """
    if not provider.input_schema:
        # Default schema if none provided
        return {
            "type": "object",
            "properties": {
                "inputs": {
                    "type": "object",
                    "description": "Input data for the workflow"
                }
            },
            "required": ["inputs"]
        }
    
    # Convert ServiceInputSchemaProperty to JSON schema
    properties = {}
    required = []
    
    for prop in provider.input_schema:
        json_prop = {
            "description": prop.description or prop.label
        }
        
        # Map types
        if prop.type == "text-input":
            json_prop["type"] = "string"
        elif prop.type == "textarea":
            json_prop["type"] = "string"
        elif prop.type == "select":
            json_prop["type"] = "string"
            if prop.enum:
                json_prop["enum"] = prop.enum
        elif prop.type == "checkbox":
            json_prop["type"] = "array"
            json_prop["items"] = {"type": "string"}
            if prop.enum:
                json_prop["items"]["enum"] = prop.enum
        else:
            json_prop["type"] = "string"
        
        if prop.default is not None:
            json_prop["default"] = prop.default
        
        properties[prop.variable_name] = json_prop
        
        if prop.required:
            required.append(prop.variable_name)
    
    return {
        "type": "object",
        "properties": {
            "inputs": {
                "type": "object",
                "properties": properties,
                "required": required
            }
        },
        "required": ["inputs"]
    }
